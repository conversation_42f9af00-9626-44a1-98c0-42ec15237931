package com.yf.exam.modules.qu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yf.exam.ability.upload.config.UploadConfig;
import com.yf.exam.core.exception.ServiceException;
import com.yf.exam.modules.qu.entity.Qu;
import com.yf.exam.modules.qu.entity.QuAnswer;
import com.yf.exam.modules.qu.service.QuAnswerService;
import com.yf.exam.modules.qu.service.QuImageService;
import com.yf.exam.modules.qu.service.QuService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * 题目图片导入导出服务实现
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
@Service
public class QuImageServiceImpl implements QuImageService {

    @Autowired
    private QuService quService;

    @Autowired
    private QuAnswerService quAnswerService;

    @Autowired
    private UploadConfig uploadConfig;

    @Override
    public void exportImages(HttpServletResponse response) throws IOException {
        exportImagesByRepo(null, response);
    }

    @Override
    public void exportImagesByRepo(String repoId, HttpServletResponse response) throws IOException {
        // 查询题目
        QueryWrapper<Qu> quWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(repoId)) {
            // 如果指定了题库ID，需要通过关联表查询
            // 这里简化处理，实际需要根据题库关联表查询
            quWrapper.lambda().isNotNull(Qu::getImage);
        }
        List<Qu> quList = quService.list(quWrapper);

        // 查询答案
        List<QuAnswer> answerList = quAnswerService.list();

        // 设置响应头
        String fileName = "exam_images_" + System.currentTimeMillis() + ".zip";
        response.setContentType("application/zip");
        response.setHeader("Content-Disposition", 
            "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            
            // 导出题目图片
            for (Qu qu : quList) {
                if (StringUtils.isNotBlank(qu.getImage())) {
                    addImageToZip(zos, qu.getImage(), "questions/qu_" + qu.getId() + "_");
                }
            }

            // 导出答案图片
            for (QuAnswer answer : answerList) {
                if (StringUtils.isNotBlank(answer.getImage())) {
                    addImageToZip(zos, answer.getImage(), "answers/answer_" + answer.getId() + "_");
                }
            }

            zos.finish();
        }
    }

    @Override
    public String importImages(MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            throw new ServiceException("上传文件不能为空");
        }

        int importCount = 0;
        int errorCount = 0;

        try (ZipInputStream zis = new ZipInputStream(file.getInputStream())) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if (!entry.isDirectory() && isImageFile(entry.getName())) {
                    try {
                        // 保存图片文件
                        String savedPath = saveImageFromZip(zis, entry.getName());
                        
                        // 更新数据库中的图片路径
                        updateImagePath(entry.getName(), savedPath);
                        
                        importCount++;
                    } catch (Exception e) {
                        log.error("导入图片失败: " + entry.getName(), e);
                        errorCount++;
                    }
                }
                zis.closeEntry();
            }
        }

        return String.format("导入完成！成功：%d 个，失败：%d 个", importCount, errorCount);
    }

    /**
     * 将图片添加到ZIP文件
     */
    private void addImageToZip(ZipOutputStream zos, String imageUrl, String prefix) throws IOException {
        // 从URL中提取文件路径
        String relativePath = imageUrl.replace(uploadConfig.getUrl(), "");
        String fullPath = uploadConfig.getDir() + relativePath;
        
        File imageFile = new File(fullPath);
        if (!imageFile.exists()) {
            log.warn("图片文件不存在: " + fullPath);
            return;
        }

        // 获取文件扩展名
        String extension = getFileExtension(imageFile.getName());
        String entryName = prefix + imageFile.getName();

        ZipEntry entry = new ZipEntry(entryName);
        zos.putNextEntry(entry);

        try (FileInputStream fis = new FileInputStream(imageFile)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) > 0) {
                zos.write(buffer, 0, length);
            }
        }

        zos.closeEntry();
    }

    /**
     * 从ZIP中保存图片
     */
    private String saveImageFromZip(ZipInputStream zis, String fileName) throws IOException {
        // 生成新的文件路径
        String extension = getFileExtension(fileName);
        String newFileName = System.currentTimeMillis() + "." + extension;
        String relativePath = "imported/" + newFileName;
        String fullPath = uploadConfig.getDir() + relativePath;

        // 确保目录存在
        Path parentDir = Paths.get(fullPath).getParent();
        if (!Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
        }

        // 保存文件
        try (FileOutputStream fos = new FileOutputStream(fullPath)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = zis.read(buffer)) > 0) {
                fos.write(buffer, 0, length);
            }
        }

        return uploadConfig.getUrl() + relativePath;
    }

    /**
     * 更新数据库中的图片路径
     */
    private void updateImagePath(String originalName, String newPath) {
        // 根据文件名规则更新对应的题目或答案
        // 这里需要根据实际的文件命名规则来实现
        // 例如：qu_123_image.jpg -> 更新题目ID为123的图片
        // answer_456_image.jpg -> 更新答案ID为456的图片
        
        if (originalName.startsWith("qu_")) {
            // 解析题目ID并更新
            String quId = extractIdFromFileName(originalName, "qu_");
            if (StringUtils.isNotBlank(quId)) {
                Qu qu = quService.getById(quId);
                if (qu != null) {
                    qu.setImage(newPath);
                    quService.updateById(qu);
                }
            }
        } else if (originalName.startsWith("answer_")) {
            // 解析答案ID并更新
            String answerId = extractIdFromFileName(originalName, "answer_");
            if (StringUtils.isNotBlank(answerId)) {
                QuAnswer answer = quAnswerService.getById(answerId);
                if (answer != null) {
                    answer.setImage(newPath);
                    quAnswerService.updateById(answer);
                }
            }
        }
    }

    /**
     * 从文件名中提取ID
     */
    private String extractIdFromFileName(String fileName, String prefix) {
        try {
            int start = fileName.indexOf(prefix) + prefix.length();
            int end = fileName.indexOf("_", start);
            if (end == -1) {
                end = fileName.lastIndexOf(".");
            }
            return fileName.substring(start, end);
        } catch (Exception e) {
            log.error("解析文件名失败: " + fileName, e);
            return null;
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf(".");
        return lastDot > 0 ? fileName.substring(lastDot + 1) : "";
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        return "jpg".equals(extension) || "jpeg".equals(extension) || "png".equals(extension);
    }
}
