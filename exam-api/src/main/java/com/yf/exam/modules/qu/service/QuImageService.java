package com.yf.exam.modules.qu.service;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 题目图片导入导出服务
 * 
 * <AUTHOR> 4.0 sonnet
 */
public interface QuImageService {

    /**
     * 导出题目图片
     * 将所有题目的图片打包下载
     * 
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void exportImages(HttpServletResponse response) throws IOException;

    /**
     * 导入题目图片
     * 从ZIP文件中导入图片并更新题目
     * 
     * @param file ZIP文件
     * @return 导入结果信息
     * @throws IOException IO异常
     */
    String importImages(MultipartFile file) throws IOException;

    /**
     * 导出指定题库的图片
     * 
     * @param repoId 题库ID
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    void exportImagesByRepo(String repoId, HttpServletResponse response) throws IOException;
}
