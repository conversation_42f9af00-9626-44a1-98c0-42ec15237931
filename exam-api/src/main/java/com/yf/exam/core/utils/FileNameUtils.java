package com.yf.exam.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文件名处理工具类
 * 
 * <AUTHOR> 4.0 sonnet
 */
@Slf4j
public class FileNameUtils {

    private static final String QU_PREFIX = "qu_";
    private static final String ANSWER_PREFIX = "answer_";
    private static final Pattern QU_PATTERN = Pattern.compile("qu_([^_]+)_(\\d+)\\.(\\w+)");
    private static final Pattern ANSWER_PATTERN = Pattern.compile("answer_([^_]+)_(\\d+)\\.(\\w+)");

    /**
     * 生成题目图片文件名
     * 
     * @param quId 题目ID
     * @param index 图片序号（从1开始）
     * @param extension 文件扩展名
     * @return 文件名
     */
    public static String generateQuestionImageName(String quId, int index, String extension) {
        if (StringUtils.isBlank(quId) || index < 1) {
            throw new IllegalArgumentException("题目ID不能为空且序号必须大于0");
        }
        
        if (StringUtils.isBlank(extension)) {
            extension = "jpg";
        }
        
        // 移除扩展名前的点号
        if (extension.startsWith(".")) {
            extension = extension.substring(1);
        }
        
        return QU_PREFIX + quId + "_" + index + "." + extension.toLowerCase();
    }

    /**
     * 生成答案图片文件名
     * 
     * @param answerId 答案ID
     * @param index 图片序号（从1开始）
     * @param extension 文件扩展名
     * @return 文件名
     */
    public static String generateAnswerImageName(String answerId, int index, String extension) {
        if (StringUtils.isBlank(answerId) || index < 1) {
            throw new IllegalArgumentException("答案ID不能为空且序号必须大于0");
        }
        
        if (StringUtils.isBlank(extension)) {
            extension = "jpg";
        }
        
        // 移除扩展名前的点号
        if (extension.startsWith(".")) {
            extension = extension.substring(1);
        }
        
        return ANSWER_PREFIX + answerId + "_" + index + "." + extension.toLowerCase();
    }

    /**
     * 从题目图片文件名中解析题目ID
     * 
     * @param fileName 文件名
     * @return 题目ID，解析失败返回null
     */
    public static String parseQuestionIdFromFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return null;
        }
        
        Matcher matcher = QU_PATTERN.matcher(fileName);
        if (matcher.matches()) {
            return matcher.group(1);
        }
        
        return null;
    }

    /**
     * 从答案图片文件名中解析答案ID
     * 
     * @param fileName 文件名
     * @return 答案ID，解析失败返回null
     */
    public static String parseAnswerIdFromFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return null;
        }
        
        Matcher matcher = ANSWER_PATTERN.matcher(fileName);
        if (matcher.matches()) {
            return matcher.group(1);
        }
        
        return null;
    }

    /**
     * 从文件名中解析图片序号
     * 
     * @param fileName 文件名
     * @return 图片序号，解析失败返回-1
     */
    public static int parseImageIndexFromFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return -1;
        }
        
        Matcher quMatcher = QU_PATTERN.matcher(fileName);
        if (quMatcher.matches()) {
            try {
                return Integer.parseInt(quMatcher.group(2));
            } catch (NumberFormatException e) {
                log.warn("解析题目图片序号失败: {}", fileName);
                return -1;
            }
        }
        
        Matcher answerMatcher = ANSWER_PATTERN.matcher(fileName);
        if (answerMatcher.matches()) {
            try {
                return Integer.parseInt(answerMatcher.group(2));
            } catch (NumberFormatException e) {
                log.warn("解析答案图片序号失败: {}", fileName);
                return -1;
            }
        }
        
        return -1;
    }

    /**
     * 判断文件名是否为题目图片
     * 
     * @param fileName 文件名
     * @return 是否为题目图片
     */
    public static boolean isQuestionImageFile(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return false;
        }
        return QU_PATTERN.matcher(fileName).matches();
    }

    /**
     * 判断文件名是否为答案图片
     * 
     * @param fileName 文件名
     * @return 是否为答案图片
     */
    public static boolean isAnswerImageFile(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return false;
        }
        return ANSWER_PATTERN.matcher(fileName).matches();
    }

    /**
     * 将文件名列表转换为逗号分隔的字符串
     * 
     * @param fileNames 文件名列表
     * @return 逗号分隔的字符串
     */
    public static String fileNamesToString(List<String> fileNames) {
        if (fileNames == null || fileNames.isEmpty()) {
            return "";
        }
        return String.join(",", fileNames);
    }

    /**
     * 将逗号分隔的字符串转换为文件名列表
     * 
     * @param fileNamesStr 逗号分隔的字符串
     * @return 文件名列表
     */
    public static List<String> stringToFileNames(String fileNamesStr) {
        if (StringUtils.isBlank(fileNamesStr)) {
            return new ArrayList<>();
        }
        
        String[] names = fileNamesStr.split(",");
        List<String> result = new ArrayList<>();
        for (String name : names) {
            String trimmed = name.trim();
            if (StringUtils.isNotBlank(trimmed)) {
                result.add(trimmed);
            }
        }
        return result;
    }

    /**
     * 生成批量题目图片文件名
     * 
     * @param quId 题目ID
     * @param imageUrls 图片URL列表
     * @return 文件名列表
     */
    public static List<String> generateQuestionImageNames(String quId, List<String> imageUrls) {
        List<String> fileNames = new ArrayList<>();
        
        if (StringUtils.isBlank(quId) || imageUrls == null || imageUrls.isEmpty()) {
            return fileNames;
        }
        
        for (int i = 0; i < imageUrls.size(); i++) {
            String url = imageUrls.get(i);
            if (StringUtils.isNotBlank(url)) {
                String extension = ImageDownloadUtils.getFileExtensionFromUrl(url);
                if (StringUtils.isBlank(extension)) {
                    extension = "jpg";
                }
                String fileName = generateQuestionImageName(quId, i + 1, extension);
                fileNames.add(fileName);
            }
        }
        
        return fileNames;
    }

    /**
     * 生成批量答案图片文件名
     * 
     * @param answerId 答案ID
     * @param imageUrls 图片URL列表
     * @return 文件名列表
     */
    public static List<String> generateAnswerImageNames(String answerId, List<String> imageUrls) {
        List<String> fileNames = new ArrayList<>();
        
        if (StringUtils.isBlank(answerId) || imageUrls == null || imageUrls.isEmpty()) {
            return fileNames;
        }
        
        for (int i = 0; i < imageUrls.size(); i++) {
            String url = imageUrls.get(i);
            if (StringUtils.isNotBlank(url)) {
                String extension = ImageDownloadUtils.getFileExtensionFromUrl(url);
                if (StringUtils.isBlank(extension)) {
                    extension = "jpg";
                }
                String fileName = generateAnswerImageName(answerId, i + 1, extension);
                fileNames.add(fileName);
            }
        }
        
        return fileNames;
    }

    /**
     * 验证文件名格式是否正确
     * 
     * @param fileName 文件名
     * @return 是否格式正确
     */
    public static boolean isValidImageFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return false;
        }
        
        return isQuestionImageFile(fileName) || isAnswerImageFile(fileName);
    }

    /**
     * 从URL列表生成文件名字符串
     * 
     * @param urls URL列表
     * @param idPrefix ID前缀（qu_或answer_）
     * @param id 题目或答案ID
     * @return 文件名字符串
     */
    public static String generateFileNamesFromUrls(List<String> urls, String idPrefix, String id) {
        if (urls == null || urls.isEmpty() || StringUtils.isBlank(id)) {
            return "";
        }
        
        List<String> fileNames = new ArrayList<>();
        for (int i = 0; i < urls.size(); i++) {
            String url = urls.get(i);
            if (StringUtils.isNotBlank(url)) {
                String extension = ImageDownloadUtils.getFileExtensionFromUrl(url);
                if (StringUtils.isBlank(extension)) {
                    extension = "jpg";
                }
                String fileName = idPrefix + id + "_" + (i + 1) + "." + extension;
                fileNames.add(fileName);
            }
        }
        
        return fileNamesToString(fileNames);
    }

    /**
     * 清理文件名，移除非法字符
     * 
     * @param fileName 原始文件名
     * @return 清理后的文件名
     */
    public static String sanitizeFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }
        
        // 移除或替换非法字符
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
    }
}
