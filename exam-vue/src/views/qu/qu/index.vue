<template>

  <div>

    <data-table
      ref="pagingTable"
      :options="options"
      :list-query="listQuery"
      @multi-actions="handleMultiAction"
    >
      <template #filter-content>

        <el-row>
          <el-col :span="24">

            <el-select v-model="listQuery.params.quType" class="filter-item" clearable>
              <el-option
                v-for="item in quTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>

            <repo-select v-model="listQuery.params.repoIds" :multi="true" />

            <el-input v-model="listQuery.params.content" placeholder="题目内容" style="width: 200px;" class="filter-item" />

            <el-button-group class="filter-item" style="float:  right">
              <el-button size="mini" icon="el-icon-download" @click="exportQuestionZip">导出ZIP包</el-button>
              <el-button size="mini" icon="el-icon-upload2" @click="showZipImport">导入ZIP包</el-button>
            </el-button-group>

          </el-col>
        </el-row>

      </template>

      <template #data-columns>

        <el-table-column
          label="题目类型"
          align="center"
          width="100px"
        >
          <template v-slot="scope">
            {{ scope.row.quType | quTypeFilter() }}
          </template>
        </el-table-column>

        <el-table-column
          label="题目内容"
          show-overflow-tooltip
        >
          <template v-slot="scope">
            <el-link type="primary" @click="handleEdit(scope.row)" :underline="false">
              {{ scope.row.content }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column
          label="题目图片"
          width="120px"
          align="center"
        >
          <template v-slot="scope">
            <multi-image-display
              :image-string="scope.row.image"
              width="50px"
              height="50px"
              :max-count="2"
            />
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="180px"
        />

        <el-table-column
          align="center"
          label="操作"
          width="120"
        >
          <template v-slot="scope">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-edit"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="info"
                size="mini"
                icon="el-icon-view"
                @click="handleView(scope.row)"
              >
                查看
              </el-button>
            </div>
          </template>
        </el-table-column>

      </template>

    </data-table>

    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="30%"
    >

      <el-form label-position="left" label-width="100px">

        <el-form-item label="操作题库" prop="repoIds">
          <repo-select v-model="dialogRepos" :multi="true" />
        </el-form-item>

        <el-row>
          <el-button type="primary" @click="handlerRepoAction">保存</el-button>
        </el-row>

      </el-form>

    </el-dialog>



    <el-dialog
      :visible.sync="imageImportVisible"
      title="导入题目图片"
      width="40%"
    >
      <div style="margin-bottom: 20px;">
        <el-alert
          title="图片导入说明"
          type="info"
          :closable="false"
          show-icon>
          <div slot="description">
            <p>1. 请上传ZIP格式的图片包</p>
            <p>2. 图片文件名格式：qu_题目ID_xxx.jpg 或 answer_答案ID_xxx.jpg</p>
            <p>3. 支持 jpg、jpeg、png 格式</p>
            <p>4. 导入前请先导出现有图片作为参考</p>
          </div>
        </el-alert>
      </div>

      <el-row style="text-align: center">
        <el-button type="primary" @click="chooseImageFile">选择图片包</el-button>
        <input ref="imageFile" class="file" name="file" type="file" accept=".zip" style="display: none" @change="doImageImport">
      </el-row>

    </el-dialog>

    <el-dialog
      :visible.sync="zipImportVisible"
      title="导入题目ZIP包"
      width="30%"
    >
      <div style="margin-bottom: 20px;">
        <el-alert
          title="仅支持导入由此处导出的题目数据ZIP包"
          type="info"
          :closable="false"
          show-icon>
          <div slot="description">
            <p>1. 请上传包含Excel和图片的ZIP文件</p>
            <p>2. ZIP包应包含：Excel文件 + images文件夹</p>
            <p>3. Excel中应包含题目图片文件名和答案图片文件名字段</p>
            <p>4. 图片文件名格式：qu_题目ID_序号.扩展名</p>
          </div>
        </el-alert>
      </div>

      <el-row style="text-align: center">
        <el-button type="primary" @click="chooseZipFile">选择ZIP包</el-button>
        <input ref="zipFile" class="file" name="file" type="file" accept=".zip" style="display: none" @change="doZipImport">
      </el-row>

    </el-dialog>

  </div>

</template>

<script>
import DataTable from '@/components/DataTable'
import RepoSelect from '@/components/RepoSelect'
import { batchAction } from '@/api/qu/repo'
import { exportQuestionZip, importQuestionZip } from '@/api/qu/qu'
import MultiImageDisplay from '@/components/MultiImageDisplay'

export default {
  name: 'QuList',
  components: { RepoSelect, DataTable, MultiImageDisplay },
  data() {
    return {

      dialogTitle: '加入题库',
      dialogVisible: false,
      imageImportVisible: false,
      zipImportVisible: false,
      dialogRepos: [],
      dialogQuIds: [],
      dialogFlag: false,

      listQuery: {
        current: 1,
        size: 10,
        params: {
          content: '',
          quType: '',
          repoIds: []
        }
      },

      quTypes: [
        {
          value: 1,
          label: '单选题'
        },
        {
          value: 2,
          label: '多选题'
        },
        {
          value: 3,
          label: '判断题'
        }
      ],

      options: {

        // 可批量操作
        multi: true,

        // 批量操作列表
        multiActions: [
          {
            value: 'add-repo',
            label: '加入题库..'
          },
          {
            value: 'remove-repo',
            label: '从..题库移除'
          },
          {
            value: 'delete',
            label: '删除'
          }
        ],
        // 列表请求URL
        listUrl: '/exam/api/qu/qu/paging',
        // 删除请求URL
        deleteUrl: '/exam/api/qu/qu/delete',
        // 添加数据路由
        addRoute: 'AddQu'
      }
    }
  },
  methods: {

    handleMultiAction(obj) {
      if (obj.opt === 'add-repo') {
        this.dialogTitle = '加入题库'
        this.dialogFlag = false
      }

      if (obj.opt === 'remove-repo') {
        this.dialogTitle = '从题库移除'
        this.dialogFlag = true
      }

      this.dialogVisible = true
      this.dialogQuIds = obj.ids
    },

    handlerRepoAction() {
      const postForm = { repoIds: this.dialogRepos, quIds: this.dialogQuIds, remove: this.dialogFlag }

      batchAction(postForm).then(() => {
        this.$notify({
          title: '成功',
          message: '批量操作成功！',
          type: 'success',
          duration: 2000
        })

        this.dialogVisible = false
        this.$refs.pagingTable.getList()
      })
    },

    // 编辑试题
    handleEdit(row) {
      this.$router.push({ name: 'UpdateQu', params: { id: row.id } })
    },

    // 查看试题详情
    handleView(row) {
      this.$router.push({ name: 'ViewQu', params: { id: row.id } })
    },



    exportQuestionZip() {
      // 获取勾选的题目ID列表
      const selectedIds = this.$refs.pagingTable.selectedIds
      if (!selectedIds || selectedIds.length === 0) {
        this.$message.warning('请先勾选要导出的题目')
        return
      }

      // 调用ZIP导出API
      this.exportQuestionZipFile(selectedIds)
    },

    exportQuestionZipFile(quIds) {
      const loading = this.$loading({
        lock: true,
        text: '正在生成ZIP文件...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      exportQuestionZip(quIds).then(response => {
        if (response.ok) {
          return response.blob()
        }
        return response.text().then(text => {
          throw new Error('导出失败: ' + text)
        })
      }).then(blob => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `questions_export_${Date.now()}.zip`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)

        this.$message.success('导出成功')
      }).catch(error => {
        this.$message.error('导出失败：' + error.message)
      }).finally(() => {
        loading.close()
      })
    },



    // 显示图片导入对话框
    showImageImport() {
      this.imageImportVisible = true
    },

    // 选择图片文件
    chooseImageFile() {
      this.$refs.imageFile.click()
    },

    // 处理图片URL（支持多个图片用逗号分隔）
    getImageUrls(imageStr) {
      if (!imageStr) return []
      return imageStr.split(',').map(url => url.trim()).filter(url => url)
    },

    // 显示ZIP导入对话框
    showZipImport() {
      this.zipImportVisible = true
    },

    // 选择ZIP文件
    chooseZipFile() {
      this.$refs.zipFile.click()
    },

    // ZIP文件导入
    doZipImport(e) {
      const file = e.target.files[0]
      if (!file) {
        return
      }

      // 验证文件格式
      if (!file.name.toLowerCase().endsWith('.zip')) {
        this.$message.error('请选择ZIP格式的文件')
        return
      }

      const loading = this.$loading({
        lock: true,
        text: '正在导入数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      importQuestionZip(file).then(res => {
        if (res.code === 0) {
          this.$message.success(res.data || '导入成功')
          this.zipImportVisible = false
          this.$refs.pagingTable.getList()
        } else {
          this.$message.error(res.msg || '导入失败')
        }
      }).catch(error => {
        this.$message.error('导入失败：' + error.message)
      }).finally(() => {
        loading.close()
        // 清空文件选择
        e.target.value = ''
      })
    },

    // 执行图片导入
    doImageImport() {
      const file = this.$refs.imageFile.files[0]
      if (!file) {
        this.$message.warning('请选择要导入的图片包')
        return
      }

      if (!file.name.toLowerCase().endsWith('.zip')) {
        this.$message.error('请选择ZIP格式的文件')
        return
      }

      const formData = new FormData()
      formData.append('file', file)

      this.$http.post(`${process.env.VUE_APP_BASE_API}/exam/api/qu/qu/import-images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(response => {
        if (response.data.code === 0) {
          this.$message.success(response.data.data || '图片导入成功')
          this.imageImportVisible = false
          // 重置文件输入
          this.$refs.imageFile.value = ''
        } else {
          this.$message.error(response.data.msg || '图片导入失败')
        }
      }).catch(error => {
        this.$message.error('图片导入失败：' + error.message)
      })
    },

    // 导出图片
    exportImages() {
      window.open(`${process.env.VUE_APP_BASE_API}/exam/api/qu/qu/export-images`, '_blank')
    }
  }
}
</script>

<style scoped>
/* 操作按钮容器样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

/* 操作按钮样式 */
.action-buttons .el-button--mini {
  width: 80px;
  margin: 0;
  font-size: 12px;
  padding: 5px 8px;
}

/* 题目内容链接样式 */
.el-link {
  font-weight: 500;
}

.el-link:hover {
  text-decoration: underline !important;
}
</style>
