<template>
  <div class="app-container">

    <el-card style="margin-top: 20px">

      <div class="qu-content">

        <p>【{{ quData.quType===1?'单选题':'多选题' }}】{{ quData.content }}</p>
        <div v-if="quData.image!=null && quData.image!=''" class="question-images">
          <el-image
            v-for="(imgUrl, index) in getImageUrls(quData.image)"
            :key="index"
            :src="imgUrl"
            class="question-image"
            fit="contain"
            :preview-src-list="getImageUrls(quData.image)"
          />
        </div>
        <div v-if="quData.quType === 1">
          <el-radio-group v-model="radioValues" readonly>
            <el-radio v-for="an in quData.answerList" :label="an.id" readonly>
              {{ an.content }}
              <div v-if="an.image!=null && an.image!=''" class="answer-images">
                <el-image
                  v-for="(imgUrl, index) in getImageUrls(an.image)"
                  :key="index"
                  :src="imgUrl"
                  class="answer-image"
                  fit="contain"
                  :preview-src-list="getImageUrls(an.image)"
                />
              </div>
            </el-radio>
          </el-radio-group>
        </div>

        <!-- 多选题 -->
        <div v-if="quData.quType === 2">
          <el-checkbox-group v-model="multiValues" readonly>
            <el-checkbox v-for="an in quData.answerList" :label="an.id">
              {{ an.content }}
              <div v-if="an.image!=null && an.image!=''" class="answer-images">
                <el-image
                  v-for="(imgUrl, index) in getImageUrls(an.image)"
                  :key="index"
                  :src="imgUrl"
                  class="answer-image"
                  fit="contain"
                  :preview-src-list="getImageUrls(an.image)"
                />
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </div>

      </div>

    </el-card>

    <el-card class="qu-analysis" style="margin-top: 20px">
      整题解析：
      <p>{{ quData.analysis }}</p>
      <p v-if="!quData.analysis">暂无解析内容！</p>
    </el-card>

    <el-card class="qu-analysis" style="margin-top: 20px; margin-bottom: 30px">
      选项解析：
      <div v-for="an in quData.answerList" v-if="an.analysis" class="qu-analysis-line">
        <p style="color: #555;">{{ an.content }}：</p>
        <div v-if="an.image!=null && an.image!=''" class="analysis-images">
          <el-image
            v-for="(imgUrl, index) in getImageUrls(an.image)"
            :key="index"
            :src="imgUrl"
            class="analysis-image"
            fit="contain"
            :preview-src-list="getImageUrls(an.image)"
          />
        </div>
        <p style="color: #1890ff;">{{ an.analysis }}</p>
      </div>
      <p v-if="analysisCount === 0">暂无选项解析</p>

    </el-card>

    <el-button type="info" @click="onCancel">返回</el-button>

  </div>
</template>

<script>
import { fetchDetail } from '@/api/qu/qu'

export default {
  name: 'QuView',
  data() {
    return {

      quData: {

      },

      radioValues: '',
      multiValues: [],
      analysisCount: 0

    }
  },
  created() {
    const id = this.$route.params.id
    if (typeof id !== 'undefined') {
      this.fetchData(id)
    }
  },
  methods: {

    // 处理图片URL（支持多个图片用逗号分隔）
    getImageUrls(imageStr) {
      if (!imageStr) return []
      return imageStr.split(',').map(url => url.trim()).filter(url => url)
    },

    fetchData(id) {
      fetchDetail(id).then(response => {
        this.quData = response.data

        this.quData.answerList.forEach((an) => {
          // 解析数量
          if (an.analysis) {
            this.analysisCount += 1
          }

          // 用户选定的
          if (an.isRight) {
            if (this.quData.quType === 1) {
              this.radioValues = an.id
            } else {
              this.multiValues.push(an.id)
            }
          }
        })
      })
    },
    onCancel() {
      this.$router.push({ name: 'ListTran' })
    }

  }
}
</script>

<style scoped>

  .qu-content{
    padding-bottom: 10px;
  }

  .qu-content div{
    line-height: 30px;
  }

  .qu-analysis p{
    color: #555; font-size: 14px
  }
  .qu-analysis-line{
    margin-top: 20px; border-bottom: #eee 1px solid
  }

  .el-checkbox-group label,.el-radio-group label{
    width: 100%;
  }

  /* 图片统一样式 */
  .question-images {
    margin: 15px 0;
    text-align: center;
  }

  .question-image {
    max-width: 600px;
    max-height: 400px;
    margin: 5px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .answer-images {
    clear: both;
    margin-top: 10px;
    margin-bottom: 5px;
  }

  .answer-image {
    max-width: 300px;
    max-height: 200px;
    margin: 3px;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  .analysis-images {
    margin: 10px 0;
  }

  .analysis-image {
    max-width: 250px;
    max-height: 150px;
    margin: 3px;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

</style>

