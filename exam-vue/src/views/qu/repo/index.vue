<template>

  <data-table
    ref="pagingTable"
    :options="options"
    :list-query="listQuery"
  >
    <template #filter-content>

      <el-input v-model="listQuery.params.title" placeholder="搜索题库名称" style="width: 200px;" class="filter-item" />

    </template>

    <template #data-columns>

      <el-table-column
        label="题库ID"
        prop="id"
        align="center"
      />

      <el-table-column
        label="题库名称"
      >

        <template slot-scope="data">

          <el-link type="primary" @click="handleEdit(data.row)" :underline="false">
            {{ data.row.title }}
          </el-link>

        </template>

      </el-table-column>

      <el-table-column
        label="单选题数量"
        prop="radioCount"
        align="center"
      />

      <el-table-column
        label="多选题数量"
        prop="multiCount"
        align="center"
      />

      <el-table-column
        label="判断题数量"
        prop="judgeCount"
        align="center"
      />

      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
      />

      <el-table-column
        align="center"
        label="操作"
        width="120"
      >
        <template slot-scope="data">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-edit"
              @click="handleEdit(data.row)"
            >
              编辑
            </el-button>
          </div>
        </template>
      </el-table-column>

    </template>

  </data-table>

</template>

<script>
import DataTable from '@/components/DataTable'

export default {
  name: 'QuList',
  components: { DataTable },
  data() {
    return {

      listQuery: {
        current: 1,
        size: 10,
        params: {
          title: ''
        }
      },

      options: {

        // 可批量操作
        multi: true,

        // 批量操作列表
        multiActions: [
          {
            value: 'delete',
            label: '删除'
          }
        ],
        // 列表请求URL
        listUrl: '/exam/api/repo/paging',
        // 删除请求URL
        deleteUrl: '/exam/api/repo/delete',
        // 启用禁用
        stateUrl: '/qu/repo/state',
        // 添加数据路由
        addRoute: 'AddRepo'
      }
    }
  },
  methods: {
    // 编辑题库
    handleEdit(row) {
      this.$router.push({ name: 'UpdateRepo', params: { id: row.id } })
    }
  }
}
</script>

<style scoped>
/* 操作按钮容器样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

/* 操作按钮样式 */
.action-buttons .el-button--mini {
  width: 80px;
  margin: 0;
  font-size: 12px;
  padding: 5px 8px;
}

/* 题库名称链接样式 */
.el-link {
  font-weight: 500;
}

.el-link:hover {
  text-decoration: underline !important;
}
</style>
