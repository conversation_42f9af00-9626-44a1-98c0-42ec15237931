import { post, upload, download } from '@/utils/request'
import { getToken } from '@/utils/auth'

/**
 * 题库详情
 * @param data
 */
export function fetchDetail(id) {
  return post('/exam/api/qu/qu/detail', { id: id })
}

/**
 * 保存题库
 * @param data
 */
export function saveData(data) {
  return post('/exam/api/qu/qu/save', data)
}



/**
 * 导出题目ZIP包
 * @param quIds 题目ID列表
 */
export function exportQuestionZip(quIds) {
  const formData = new FormData()
  quIds.forEach(id => {
    formData.append('quIds', id)
  })

  return fetch('/exam/api/qu/qu/export-zip', {
    method: 'POST',
    headers: {
      'token': getToken() || ''
    },
    body: formData
  })
}

/**
 * 导入题目ZIP包
 * @param file ZIP文件
 */
export function importQuestionZip(file) {
  return upload('/exam/api/qu/qu/import-zip', file)
}

