#!/bin/bash

# 考试系统备份脚本
# 包含数据库和上传文件的完整备份

# 配置
BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="exam_system_backup_${DATE}"

# 创建备份目录
mkdir -p ${BACKUP_DIR}

echo "开始备份考试系统..."

# 1. 备份数据库（需要根据实际数据库配置修改）
echo "备份数据库..."
# mysqldump -u username -p password database_name > ${BACKUP_DIR}/${BACKUP_NAME}_database.sql

# 2. 备份上传文件
echo "备份上传文件..."
if [ -d "./upload" ]; then
    tar -czf ${BACKUP_DIR}/${BACKUP_NAME}_uploads.tar.gz ./upload
    echo "上传文件备份完成: ${BACKUP_DIR}/${BACKUP_NAME}_uploads.tar.gz"
else
    echo "警告: upload目录不存在"
fi

# 3. 备份配置文件
echo "备份配置文件..."
tar -czf ${BACKUP_DIR}/${BACKUP_NAME}_config.tar.gz \
    exam-api/src/main/resources/application*.yml \
    exam-vue/.env* \
    2>/dev/null

# 4. 创建完整备份包
echo "创建完整备份包..."
cd ${BACKUP_DIR}
tar -czf ${BACKUP_NAME}_complete.tar.gz ${BACKUP_NAME}_*
cd ..

echo "备份完成!"
echo "备份文件位置: ${BACKUP_DIR}/${BACKUP_NAME}_complete.tar.gz"
echo ""
echo "恢复说明:"
echo "1. 解压备份文件"
echo "2. 恢复数据库: mysql -u username -p database_name < backup_database.sql"
echo "3. 解压上传文件到项目根目录"
echo "4. 恢复配置文件"
